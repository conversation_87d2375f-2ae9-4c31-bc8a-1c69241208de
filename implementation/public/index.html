<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Localhost File Tailer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .header p {
            color: #cccccc;
            font-size: 14px;
        }

        .controls {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: center;
        }

        .file-input-wrapper {
            position: relative;
        }

        .file-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #444;
            border-radius: 4px;
            background: #1e1e1e;
            color: #ffffff;
            font-size: 14px;
        }

        .file-input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .file-browse {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .file-browse:hover {
            background: #45a049;
        }

        .toggle-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background 0.3s;
            min-width: 100px;
        }

        .toggle-btn:hover {
            background: #1976D2;
        }

        .toggle-btn.stop {
            background: #f44336;
        }

        .toggle-btn.stop:hover {
            background: #d32f2f;
        }

        .toggle-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .stats {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #cccccc;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .content-area {
            background: #1e1e1e;
            border: 2px solid #444;
            border-radius: 8px;
            height: 500px;
            position: relative;
            overflow: hidden;
        }

        .content-textarea {
            width: 100%;
            height: 100%;
            background: transparent;
            border: none;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            padding: 15px;
            resize: none;
            outline: none;
            overflow-y: auto;
        }

        .content-textarea::-webkit-scrollbar {
            width: 8px;
        }

        .content-textarea::-webkit-scrollbar-track {
            background: #2d2d2d;
        }

        .content-textarea::-webkit-scrollbar-thumb {
            background: #4CAF50;
            border-radius: 4px;
        }

        .status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #4CAF50;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.stopped {
            color: #f44336;
        }

        .error {
            background: #f44336;
            color: white;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }

        @media (max-width: 768px) {
            .controls {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Localhost File Tailer</h1>
            <p>Real-time file monitoring with advanced time-based seeking</p>
        </div>

        <div class="error" id="errorMessage"></div>

        <div class="controls">
            <div class="file-input-wrapper">
                <select id="fileSelect" class="file-input">
                    <option value="">Select a file...</option>
                </select>
            </div>
            <button id="refreshBtn" class="file-browse">Refresh</button>
            <button id="toggleBtn" class="toggle-btn" disabled>Start Tailing</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <span id="bytesProcessed" class="stat-value">0</span>
                <span class="stat-label">Bytes Processed</span>
            </div>
            <div class="stat-item">
                <span id="linesCount" class="stat-value">0</span>
                <span class="stat-label">Lines</span>
            </div>
            <div class="stat-item">
                <span id="updateCount" class="stat-value">0</span>
                <span class="stat-label">Updates</span>
            </div>
            <div class="stat-item">
                <span id="fileSize" class="stat-value">0</span>
                <span class="stat-label">File Size</span>
            </div>
        </div>

        <div class="content-area">
            <div id="status" class="status stopped">STOPPED</div>
            <textarea id="contentTextarea" class="content-textarea" readonly placeholder="Select a file and click 'Start Tailing' to begin monitoring..."></textarea>
        </div>
    </div>

    <script>
        class FileTailerUI {
            constructor() {
                this.isRunning = false;
                this.bytesProcessed = 0;
                this.linesCount = 0;
                this.updateCount = 0;
                this.currentFilePath = '';
                this.updateInterval = null;

                this.initializeElements();
                this.bindEvents();
                this.loadAvailableFiles();
            }

            initializeElements() {
                this.fileSelect = document.getElementById('fileSelect');
                this.refreshBtn = document.getElementById('refreshBtn');
                this.toggleBtn = document.getElementById('toggleBtn');
                this.contentTextarea = document.getElementById('contentTextarea');
                this.status = document.getElementById('status');
                this.errorMessage = document.getElementById('errorMessage');

                // Stats elements
                this.bytesProcessedEl = document.getElementById('bytesProcessed');
                this.linesCountEl = document.getElementById('linesCount');
                this.updateCountEl = document.getElementById('updateCount');
                this.fileSizeEl = document.getElementById('fileSize');
            }

            bindEvents() {
                this.fileSelect.addEventListener('change', () => this.onFileChange());
                this.refreshBtn.addEventListener('click', () => this.loadAvailableFiles());
                this.toggleBtn.addEventListener('click', () => this.onToggleClick());
            }

            async loadAvailableFiles() {
                try {
                    const response = await fetch('/api/files');
                    const data = await response.json();

                    this.fileSelect.innerHTML = '<option value="">Select a file...</option>';

                    data.files.forEach(file => {
                        const option = document.createElement('option');
                        option.value = file;
                        option.textContent = file;
                        this.fileSelect.appendChild(option);
                    });

                    if (data.files.length === 0) {
                        this.showError('No log files found. Create a .log or .txt file in the implementation directory.');
                    }

                } catch (error) {
                    this.showError('Failed to load available files: ' + error.message);
                }
            }

            onFileChange() {
                const filePath = this.fileSelect.value;
                this.toggleBtn.disabled = !filePath;
                this.currentFilePath = filePath;
            }

            async onToggleClick() {
                if (this.isRunning) {
                    await this.stopTailing();
                } else {
                    await this.startTailing();
                }
            }

            async startTailing() {
                try {
                    this.hideError();

                    // Clear textarea and reset stats
                    this.contentTextarea.value = '';
                    this.resetStats();

                    // Start tailing via API
                    const response = await fetch('/api/tail/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            filePath: this.currentFilePath
                        })
                    });

                    const data = await response.json();

                    if (!response.ok) {
                        throw new Error(data.error);
                    }

                    // Update initial content
                    this.contentTextarea.value = data.initialContent;
                    this.bytesProcessed = data.initialContent.length;
                    this.linesCount = data.initialContent.split('\n').length - 1;
                    this.updateStats();

                    // Update UI state
                    this.isRunning = true;
                    this.toggleBtn.textContent = 'Stop Tailing';
                    this.toggleBtn.classList.add('stop');
                    this.status.textContent = 'RUNNING';
                    this.status.classList.remove('stopped');
                    this.fileSelect.disabled = true;
                    this.refreshBtn.disabled = true;

                    // Start polling for updates
                    this.startUpdatePolling();

                    this.showSuccess('Started tailing: ' + this.currentFilePath);

                } catch (error) {
                    this.showError('Failed to start tailing: ' + error.message);
                }
            }

            async stopTailing() {
                try {
                    // Stop polling
                    if (this.updateInterval) {
                        clearInterval(this.updateInterval);
                        this.updateInterval = null;
                    }

                    // Stop tailing via API
                    await fetch('/api/tail/stop', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            filePath: this.currentFilePath
                        })
                    });

                    // Update UI state
                    this.isRunning = false;
                    this.toggleBtn.textContent = 'Start Tailing';
                    this.toggleBtn.classList.remove('stop');
                    this.status.textContent = 'STOPPED';
                    this.status.classList.add('stopped');
                    this.fileSelect.disabled = false;
                    this.refreshBtn.disabled = false;

                    this.showSuccess('Stopped tailing');

                } catch (error) {
                    this.showError('Error stopping tailer: ' + error.message);
                }
            }

            startUpdatePolling() {
                this.updateInterval = setInterval(async () => {
                    try {
                        const response = await fetch(`/api/tail/updates/${encodeURIComponent(this.currentFilePath)}`);
                        const update = await response.json();

                        if (response.ok && update.newContent) {
                            this.onFileUpdate(update);
                        }
                    } catch (error) {
                        console.error('Error polling for updates:', error);
                    }
                }, 500); // Poll every 500ms
            }

            onFileUpdate(update) {
                if (update.newContent) {
                    // Append new content
                    this.contentTextarea.value += update.newContent;

                    // Update stats
                    this.bytesProcessed += update.newContent.length;
                    this.linesCount += (update.newContent.match(/\n/g) || []).length;
                    this.updateCount++;

                    // Auto-scroll to bottom
                    this.contentTextarea.scrollTop = this.contentTextarea.scrollHeight;

                    this.updateStats();
                }
            }

            resetStats() {
                this.bytesProcessed = 0;
                this.linesCount = 0;
                this.updateCount = 0;
                this.updateStats();
            }

            updateStats() {
                this.bytesProcessedEl.textContent = this.formatBytes(this.bytesProcessed);
                this.linesCountEl.textContent = this.linesCount.toLocaleString();
                this.updateCountEl.textContent = this.updateCount.toLocaleString();
                
                // File size will be updated when we get updates from the server
            }

            formatBytes(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            showError(message) {
                this.errorMessage.textContent = message;
                this.errorMessage.style.display = 'block';
                setTimeout(() => this.hideError(), 5000);
            }

            showSuccess(message) {
                // You could add a success message area if needed
                console.log('Success:', message);
            }

            hideError() {
                this.errorMessage.style.display = 'none';
            }
        }

        // Initialize the UI when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new FileTailerUI();
        });
    </script>
</body>
</html>
