{"name": "localhost-file-tailer", "version": "1.0.0", "description": "A web-based file tailer with real-time updates and time-based seeking", "main": "src/index.js", "type": "module", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:performance": "jest tests/performance", "lint": "eslint src tests", "lint:fix": "eslint src tests --fix", "dev": "node src/index.js", "start": "node src/index.js"}, "keywords": ["file-tailer", "real-time", "web-interface", "file-monitoring"], "author": "", "license": "MIT", "devDependencies": {"@jest/globals": "^29.7.0", "eslint": "^8.57.0", "jest": "^29.7.0", "supertest": "^6.3.4", "ws": "^8.16.0"}, "dependencies": {"express": "^4.18.2", "chokidar": "^3.5.3"}, "jest": {"transform": {}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/**/*.test.js"], "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}, "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "eslintConfig": {"env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "no-var": "error"}}}