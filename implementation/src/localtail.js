/**
 * Local file tailer implementation
 * Provides real-time file tailing with efficient seeking and updates
 */

import fs from 'fs/promises';
import { createReadStream } from 'fs';
import { watch } from 'chokidar';
import { EventEmitter } from 'events';
import path from 'path';

/**
 * File tailer class that handles file watching and content updates
 */
class FileTailer extends EventEmitter {
  constructor(filePath, options = {}) {
    super();
    this.filePath = filePath;
    this.options = {
      encoding: 'utf8',
      maxLines: null,
      fromEnd: false,
      lines: null,
      pollInterval: 100,
      onUpdate: null,
      ...options
    };
    this.position = 0;
    this.content = '';
    this.size = 0;
    this.lastModified = null;
    this.watcher = null;
    this.isBinary = false;
  }

  async initialize() {
    // Validate file path
    if (!this.filePath || typeof this.filePath !== 'string' || this.filePath.trim() === '') {
      throw new Error('Invalid file path');
    }

    try {
      // Check if file exists and get stats
      const stats = await fs.stat(this.filePath);
      this.size = stats.size;
      this.lastModified = new Date(stats.mtime); // Ensure it's a Date object

      // Read initial content
      await this.readContent();

      // Set up file watching
      this.setupWatcher();

      return this;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error('File not found');
      }
      throw error;
    }
  }

  async readContent() {
    try {
      const buffer = await fs.readFile(this.filePath);

      // Check if file is binary
      this.isBinary = this.detectBinary(buffer);

      if (this.isBinary) {
        this.content = buffer.toString('hex');
        this.warning = 'Binary file detected - content shown as hex';
        return;
      }

      let content = buffer.toString(this.options.encoding);

      // Handle maxLines option
      if (this.options.maxLines) {
        const lines = content.split('\n');
        if (this.options.fromEnd) {
          content = lines.slice(-this.options.maxLines).join('\n');
        } else {
          content = lines.slice(0, this.options.maxLines).join('\n');
        }
      }

      // Handle fromEnd with lines option
      if (this.options.fromEnd && this.options.lines) {
        const lines = content.split('\n');
        // Filter out empty lines at the end and take the requested number
        const nonEmptyLines = lines.filter(line => line.trim() !== '');
        content = nonEmptyLines.slice(-this.options.lines).join('\n');
        if (content && !content.endsWith('\n')) {
          content += '\n';
        }
      }

      this.content = content;
      this.position = buffer.length;
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  detectBinary(buffer) {
    // Simple binary detection - check for null bytes
    for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
      if (buffer[i] === 0) {
        return true;
      }
    }
    return false;
  }

  setupWatcher() {
    this.watcher = watch(this.filePath, {
      persistent: true,
      usePolling: false,
      interval: this.options.pollInterval
    });

    this.watcher.on('change', async () => {
      try {
        await this.handleFileChange();
      } catch (error) {
        // Only emit error if we have listeners to prevent unhandled errors
        if (this.listenerCount('error') > 0) {
          this.emit('error', error);
        }
      }
    });

    this.watcher.on('unlink', () => {
      // Only emit error if we have listeners to prevent unhandled errors
      if (this.listenerCount('error') > 0) {
        this.emit('error', new Error('File no longer exists'));
      }
    });
  }

  async handleFileChange() {
    try {
      const stats = await fs.stat(this.filePath);

      if (stats.size > this.position) {
        // File has grown, read new content from our current position
        const newContent = await this.readNewContent(this.position, stats.size);
        this.content += newContent;
        this.size = stats.size;
        this.position = stats.size;
        this.lastModified = new Date(stats.mtime);

        const update = {
          newContent,
          totalContent: this.content,
          position: this.position,
          size: this.size
        };

        if (this.options.onUpdate) {
          this.options.onUpdate(update);
        }

        this.emit('update', update);
      }
    } catch (error) {
      // Only emit error if we have listeners to prevent unhandled errors
      if (this.listenerCount('error') > 0) {
        this.emit('error', error);
      }
    }
  }

  async readNewContent(start, end) {
    return new Promise((resolve, reject) => {
      const stream = createReadStream(this.filePath, {
        start,
        end: end - 1,
        encoding: this.options.encoding
      });

      let content = '';
      stream.on('data', chunk => {
        content += chunk;
      });

      stream.on('end', () => {
        resolve(content);
      });

      stream.on('error', reject);
    });
  }

  async getUpdates() {
    try {
      // Force a fresh stat check to ensure we get the latest file size
      const stats = await fs.stat(this.filePath);

      if (stats.size > this.position) {
        const newContent = await this.readNewContent(this.position, stats.size);
        this.content += newContent;
        this.size = stats.size;
        this.position = stats.size;
        this.lastModified = new Date(stats.mtime);

        return {
          newContent,
          totalContent: this.content,
          position: this.position,
          size: this.size
        };
      }

      return {
        newContent: '',
        totalContent: this.content,
        position: this.position,
        size: this.size
      };
    } catch (error) {
      return {
        error: `File no longer exists: ${error.message}`
      };
    }
  }

  handleError(error) {
    this.emit('error', error);
  }

  close() {
    if (this.watcher) {
      this.watcher.close();
    }
  }
}

/**
 * Tail a local file with real-time updates
 * @param {string} filePath - Path to the file to tail
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Tailer object with file content and methods
 */
export async function localtail(filePath, options = {}) {
  const tailer = new FileTailer(filePath, options);
  await tailer.initialize();

  return {
    content: tailer.content,
    position: tailer.position,
    filePath: tailer.filePath,
    size: tailer.size,
    lastModified: tailer.lastModified,
    isBinary: tailer.isBinary,
    warning: tailer.warning,
    encoding: tailer.options.encoding,
    pollInterval: tailer.options.pollInterval,
    getUpdates: () => tailer.getUpdates(),
    handleError: (error) => tailer.handleError(error),
    close: () => tailer.close(),
    on: (event, callback) => tailer.on(event, callback),
    off: (event, callback) => tailer.off(event, callback)
  };
}

/**
 * Seek to a relative time in a file and return content from that point
 * @param {string} filePath - Path to the file to seek in
 * @param {string} timeExpression - Relative time expression (e.g., "5 minutes ago", "last 10 minutes")
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Seek result with content and metadata
 */
export async function localtail_seek_relative_time(filePath, timeExpression, options = {}) {
  // This is a placeholder implementation
  // Tests will fail and guide us to implement the real functionality
  throw new Error('Not implemented yet');
}
