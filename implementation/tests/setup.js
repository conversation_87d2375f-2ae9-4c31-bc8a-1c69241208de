/**
 * Jest test setup file
 * Configures global test environment and utilities
 */

import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Global test timeout
jest.setTimeout(30000);

// Test utilities available globally
global.testUtils = {
  /**
   * Create a temporary test file with content
   */
  async createTestFile(filename, content = '') {
    const testDir = path.join(__dirname, 'fixtures', 'temp');
    await fs.mkdir(testDir, { recursive: true });
    const filePath = path.join(testDir, filename);
    await fs.writeFile(filePath, content);
    return filePath;
  },

  /**
   * Clean up temporary test files
   */
  async cleanupTestFiles() {
    const tempDir = path.join(__dirname, 'fixtures', 'temp');
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  },

  /**
   * Generate test content with timestamps
   */
  generateTimestampedContent(lines = 10, intervalMs = 1000) {
    const content = [];
    const startTime = Date.now();
    
    for (let i = 0; i < lines; i++) {
      const timestamp = new Date(startTime + (i * intervalMs)).toISOString();
      content.push(`${timestamp} Log line ${i + 1}: Sample log entry`);
    }
    
    return content.join('\n');
  },

  /**
   * Generate large test file content
   */
  generateLargeContent(sizeInMB = 1) {
    const lineSize = 100; // Approximate bytes per line
    const linesNeeded = Math.floor((sizeInMB * 1024 * 1024) / lineSize);
    const lines = [];
    
    for (let i = 0; i < linesNeeded; i++) {
      const timestamp = new Date(Date.now() + (i * 1000)).toISOString();
      const padding = 'x'.repeat(lineSize - timestamp.length - 20);
      lines.push(`${timestamp} Line ${i.toString().padStart(6, '0')} ${padding}`);
    }
    
    return lines.join('\n');
  },

  /**
   * Wait for a specified amount of time
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Measure execution time of a function
   */
  async measureTime(fn) {
    const start = process.hrtime.bigint();
    const result = await fn();
    const end = process.hrtime.bigint();
    const durationMs = Number(end - start) / 1000000; // Convert to milliseconds
    
    return {
      result,
      durationMs
    };
  }
};

// Global cleanup after each test
afterEach(async () => {
  await global.testUtils.cleanupTestFiles();
});

// Global cleanup after all tests
afterAll(async () => {
  await global.testUtils.cleanupTestFiles();
});
