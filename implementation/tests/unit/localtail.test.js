/**
 * Unit tests for localtail() function
 * Following TDD approach - these tests define the expected behavior
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { localtail } from '../../src/localtail.js';
import fs from 'fs/promises';
import path from 'path';

describe('localtail()', () => {
  let testFilePath;
  let tailers = [];

  beforeEach(async () => {
    // Create a test file for each test
    testFilePath = await global.testUtils.createTestFile('test.log', 'Initial content\n');
    tailers = [];
  });

  afterEach(async () => {
    // Clean up all tailers to prevent unhandled errors
    for (const tailer of tailers) {
      if (tailer && tailer.close) {
        tailer.close();
      }
    }
    tailers = [];
    await global.testUtils.cleanupTestFiles();
  });

  describe('Basic functionality', () => {
    test('should throw error for non-existent file', async () => {
      const nonExistentPath = '/path/to/nonexistent/file.log';
      
      await expect(localtail(nonExistentPath)).rejects.toThrow('File not found');
    });

    test('should throw error for invalid file path', async () => {
      await expect(localtail(null)).rejects.toThrow('Invalid file path');
      await expect(localtail('')).rejects.toThrow('Invalid file path');
      await expect(localtail(123)).rejects.toThrow('Invalid file path');
    });

    test('should return initial file content', async () => {
      const result = await localtail(testFilePath);
      
      expect(result).toHaveProperty('content');
      expect(result.content).toBe('Initial content\n');
      expect(result).toHaveProperty('position');
      expect(result.position).toBeGreaterThan(0);
    });

    test('should return file metadata', async () => {
      const result = await localtail(testFilePath);
      
      expect(result).toHaveProperty('filePath', testFilePath);
      expect(result).toHaveProperty('size');
      expect(result).toHaveProperty('lastModified');
      expect(result.size).toBeGreaterThan(0);
      expect(result.lastModified).toBeInstanceOf(Date);
    });
  });

  describe('File watching and updates', () => {
    test('should detect new content appended to file', async () => {
      let updateReceived = null;
      const updatePromise = new Promise(resolve => {
        updateReceived = resolve;
      });

      const tailer = await localtail(testFilePath, {
        onUpdate: (update) => updateReceived(update)
      });
      tailers.push(tailer);
      const initialContent = tailer.content;

      // Append new content to the file
      await fs.appendFile(testFilePath, 'New line added\n');

      // Wait for the update callback
      const update = await updatePromise;

      expect(update.newContent).toBe('New line added\n');
      expect(update.totalContent).toBe(initialContent + 'New line added\n');
    });

    test('should handle multiple rapid updates', async () => {
      const updates = [];
      const tailer = await localtail(testFilePath, {
        onUpdate: (update) => updates.push(update)
      });
      tailers.push(tailer);

      // Add multiple lines rapidly
      for (let i = 1; i <= 5; i++) {
        await fs.appendFile(testFilePath, `Line ${i}\n`);
        // Small delay between writes to ensure they're detected
        await global.testUtils.wait(50);
      }

      // Wait for all updates to be processed
      await global.testUtils.wait(200);

      // Check that we received updates
      expect(updates.length).toBeGreaterThan(0);

      // Combine all new content from updates
      const allNewContent = updates.map(u => u.newContent).join('');
      expect(allNewContent).toContain('Line 1\n');
      expect(allNewContent).toContain('Line 5\n');
    });

    test('should provide callback mechanism for real-time updates', async () => {
      const updates = [];
      const callback = (update) => updates.push(update);

      const tailer = await localtail(testFilePath, { onUpdate: callback });
      tailers.push(tailer);

      await fs.appendFile(testFilePath, 'Callback test\n');
      await global.testUtils.wait(100);

      expect(updates).toHaveLength(1);
      expect(updates[0].newContent).toBe('Callback test\n');
    });
  });

  describe('Performance requirements', () => {
    test('should handle large files efficiently', async () => {
      // Create a large test file (1MB+)
      const largeContent = global.testUtils.generateLargeContent(1);
      const largeFilePath = await global.testUtils.createTestFile('large.log', largeContent);
      
      const { result, durationMs } = await global.testUtils.measureTime(async () => {
        return await localtail(largeFilePath);
      });
      
      expect(result).toHaveProperty('content');
      expect(durationMs).toBeLessThan(1000); // Should complete within 1 second
    });

    test('should seek to end of file efficiently for large files', async () => {
      const largeContent = global.testUtils.generateLargeContent(2);
      const largeFilePath = await global.testUtils.createTestFile('large.log', largeContent);
      
      const { result, durationMs } = await global.testUtils.measureTime(async () => {
        return await localtail(largeFilePath, { fromEnd: true, lines: 10 });
      });
      
      expect(result.content.split('\n')).toHaveLength(11); // 10 lines + empty string from split
      expect(durationMs).toBeLessThan(100); // Should complete within 100ms
    });
  });

  describe('Error handling and edge cases', () => {
    test('should handle file deletion during tailing', async () => {
      const tailer = await localtail(testFilePath);
      tailers.push(tailer);

      // Delete the file
      await fs.unlink(testFilePath);

      // Should handle gracefully
      const result = await tailer.getUpdates();
      expect(result).toHaveProperty('error');
      expect(result.error).toContain('File no longer exists');
    });

    test('should handle permission errors', async () => {
      // This test might be platform-specific
      // For now, we'll test the error handling structure
      const tailer = await localtail(testFilePath);
      expect(tailer).toHaveProperty('handleError');
      expect(typeof tailer.handleError).toBe('function');
    });

    test('should handle binary files appropriately', async () => {
      // Create a binary file (simulate with non-UTF8 content)
      const binaryContent = Buffer.from([0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE]);
      const binaryPath = await global.testUtils.createTestFile('binary.dat', binaryContent);
      
      const result = await localtail(binaryPath);
      expect(result).toHaveProperty('isBinary', true);
      expect(result).toHaveProperty('warning');
    });
  });

  describe('Configuration options', () => {
    test('should respect maxLines option', async () => {
      const multiLineContent = Array.from({ length: 20 }, (_, i) => `Line ${i + 1}`).join('\n');
      const multiLinePath = await global.testUtils.createTestFile('multiline.log', multiLineContent);
      
      const result = await localtail(multiLinePath, { maxLines: 5 });
      const lines = result.content.trim().split('\n');
      expect(lines).toHaveLength(5);
    });

    test('should respect encoding option', async () => {
      const result = await localtail(testFilePath, { encoding: 'utf8' });
      expect(result).toHaveProperty('encoding', 'utf8');
    });

    test('should respect polling interval option', async () => {
      const tailer = await localtail(testFilePath, { pollInterval: 50 });
      expect(tailer).toHaveProperty('pollInterval', 50);
    });
  });
});
